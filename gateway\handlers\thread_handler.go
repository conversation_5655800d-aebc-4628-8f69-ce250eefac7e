package handlers

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	attendancepb "github.com/olzzhas/edunite-server/course_service/pb/attendance"
	semesterpb "github.com/olzzhas/edunite-server/course_service/pb/semester"
	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"
	transcriptpb "github.com/olzzhas/edunite-server/course_service/pb/transcript"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/gin-gonic/gin"
	"github.com/olzzhas/edunite-server/gateway/clients"
)

// ThreadHandler обрабатывает HTTP-запросы, связанные с потоками
type ThreadHandler struct {
	RabbitLogPublisher clients.LogPublisher
	CourseService      *clients.CourseClient
	StorageService     *clients.StorageClient
	ThreadService      *clients.ThreadClient
	AttendanceService  *clients.AttendanceClient
	UserService        *clients.UserClient
	SemesterService    *clients.SemesterClient
	TranscriptService  *clients.TranscriptClient
}

// CreateThreadHandler создает новый поток и загружает файл syllabus (опционально)
func (h *ThreadHandler) CreateThreadHandler(c *gin.Context) {
	c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 2<<20) // 2 MB

	if err := c.Request.ParseMultipartForm(2 << 20); err != nil {
		if strings.Contains(err.Error(), "request body too large") {
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "file size exceeds 2MB limit"})
			return
		}
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing form data: %v", err),
			"thread",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
		return
	}

	title := c.PostForm("title")
	description := c.PostForm("description")
	courseID := c.PostForm("course_id")
	semesterID := c.PostForm("semester_id")
	teacherID := c.PostForm("teacher_id")
	maxStudents := toInt32(c.PostForm("max_students"))

	if title == "" || description == "" || courseID == "" || semesterID == "" || teacherID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "title, description, course_id, semester_id, and teacher_id are required"})
		return
	}

	var syllabusURL string

	// Пытаемся извлечь файл syllabus (опционально)
	file, header, err := c.Request.FormFile("syllabus")
	if err != nil {
		if errors.Is(err, http.ErrMissingFile) {
			// Файл не был загружен, продолжаем без него
			// syllabusURL останется пустым
		} else {
			// Другая ошибка при получении файла
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error while parsing syllabus file: %v", err),
				"thread",
				map[string]any{"title": title, "error": err.Error()},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "failed to process syllabus file"})
			return
		}
	} else {
		defer file.Close()

		// Определяем MIME-тип
		contentType := header.Header.Get("Content-Type")
		if contentType == "" {
			contentType = "application/octet-stream"
		}

		// Разрешаем только изображения и PDF (при необходимости)
		allowedTypes := []string{"image/jpeg", "image/png", "application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}
		isAllowed := false
		for _, t := range allowedTypes {
			if contentType == t {
				isAllowed = true
				break
			}
		}
		if !isAllowed {
			c.JSON(http.StatusUnsupportedMediaType, gin.H{"error": "only JPEG, PNG images and PDF, DOCX files are allowed for syllabus"})
			return
		}

		// Проверяем размер файла (уже ограничен MaxBytesReader, но дополнительно проверим)
		fileSize := header.Size
		if fileSize > 2<<20 { // 2 MB
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "syllabus file size exceeds 2MB limit"})
			return
		}

		// Читаем содержимое файла
		fileData := make([]byte, fileSize)
		_, err := file.Read(fileData)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error reading syllabus file: %v", err),
				"thread",
				map[string]any{"title": title, "error": err.Error()},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to read syllabus file"})
			return
		}

		// Обрабатываем имя файла (заменяем пробелы и другие недопустимые символы)
		originalFilename := header.Filename
		safeFilename := strings.ReplaceAll(originalFilename, " ", "_")
		// Дополнительно можно использовать функции для более тщательной санитации имени

		objectName := fmt.Sprintf("%d_%s", time.Now().UnixNano(), safeFilename)

		// Загружаем файл в StorageService
		_, err = h.StorageService.UploadFile(
			c.Request.Context(),
			"syllabus-files", // Название bucket для syllabus
			objectName,
			contentType,
			fileData,
		)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error uploading syllabus file: %v", err),
				"thread",
				map[string]any{"title": title, "error": err.Error()},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to upload syllabus file"})
			return
		}

		// Устанавливаем URL загруженного syllabus
		syllabusURL = fmt.Sprintf("syllabus-files/%s", objectName) // Предполагается, что UploadFile возвращает URL
	}

	// Формируем запрос для создания потока
	threadReq := &threadpb.ThreadRequest{
		CourseId:    toInt64(courseID),
		SemesterId:  toInt64(semesterID),
		TeacherId:   toInt64(teacherID),
		MaxStudents: maxStudents,
		Title:       title,
		SyllabusUrl: syllabusURL,
	}

	// Отправляем запрос в ThreadService
	threadResp, err := h.ThreadService.CreateThread(c.Request.Context(), threadReq)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error creating thread: %v", err),
			"thread",
			map[string]any{
				"title":       title,
				"description": description,
				"error":       err.Error(),
			},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create thread"})
		return
	}

	// Логируем успешное создание потока
	h.RabbitLogPublisher.PublishLog(
		"info",
		fmt.Sprintf("Thread created successfully: %d", threadResp.Id),
		"thread",
		map[string]any{
			"title":       title,
			"description": description,
			"thread_id":   fmt.Sprintf("%d", threadResp.Id),
		},
	)

	// Отправляем успешный ответ
	c.JSON(http.StatusCreated, gin.H{
		"message":      "Thread created successfully",
		"thread_id":    threadResp.Id,
		"syllabus_url": syllabusURL,
	})
}

// ListThreadsHandler получает список всех потоков с информацией о преподавателях, курсах и расписаниях
func (h *ThreadHandler) ListThreadsHandler(c *gin.Context) {
	// Создаем пустой запрос
	req := &threadpb.ThreadEmptyRequest{}

	// Отправляем запрос в ThreadService
	resp, err := h.ThreadService.ListThreads(c.Request.Context(), req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error listing threads: %v", err),
			"thread",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list threads"})
		return
	}

	// Собираем уникальные ID преподавателей и курсов
	teacherIDs := make(map[int64]struct{})
	courseIDs := make(map[int64]struct{})
	threadIDs := make([]int64, 0, len(resp.Threads))
	for _, thread := range resp.Threads {
		teacherIDs[thread.TeacherId] = struct{}{}
		courseIDs[thread.CourseId] = struct{}{}
		threadIDs = append(threadIDs, thread.Id)
	}

	// Получаем информацию о преподавателях
	teachers := make(map[int64]*teacherDTO)
	for teacherID := range teacherIDs {
		teacher, err := h.UserService.GetUser(teacherID)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error fetching teacher info: %v", err),
				"thread",
				map[string]any{"teacher_id": teacherID, "error": err.Error()},
			)
			// Продолжаем выполнение даже если не удалось получить информацию о преподавателе
			continue
		}
		teachers[teacherID] = &teacherDTO{
			Id:      teacher.GetId(),
			Name:    teacher.GetName(),
			Surname: teacher.GetSurname(),
			Email:   teacher.GetEmail(),
		}
	}

	// Получаем информацию о курсах
	courses := make(map[int64]*threadpb.CourseInfo)
	for courseID := range courseIDs {
		course, err := h.CourseService.GetCourseByID(c.Request.Context(), courseID)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error fetching course info: %v", err),
				"thread",
				map[string]any{"course_id": courseID, "error": err.Error()},
			)
			// Продолжаем выполнение даже если не удалось получить информацию о курсе
			continue
		}
		courses[courseID] = &threadpb.CourseInfo{
			Id:             course.GetId(),
			Title:          course.GetTitle(),
			Description:    course.GetDescription(),
			BannerImageUrl: course.GetBannerImageUrl(),
		}
	}

	// Получаем расписания для всех потоков
	schedules := make(map[int64][]*threadpb.ThreadScheduleResponse)
	for _, threadID := range threadIDs {
		scheduleReq := &threadpb.ThreadSchedulesRequest{
			ThreadId: threadID,
		}
		scheduleResp, err := h.ThreadService.ListThreadSchedules(c.Request.Context(), scheduleReq)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error getting thread schedules: %v", err),
				"thread",
				map[string]any{"thread_id": threadID, "error": err.Error()},
			)
			// Продолжаем выполнение даже если не удалось получить расписания
			continue
		}
		schedules[threadID] = scheduleResp.Schedules
	}

	// Получаем информацию о регистрациях для всех потоков
	registrations := make(map[int64]int)
	for _, threadID := range threadIDs {
		registrationsResp, err := h.ThreadService.ListThreadRegistrations(c.Request.Context(), threadID)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error getting thread registrations: %v", err),
				"thread",
				map[string]any{"thread_id": threadID, "error": err.Error()},
			)
			// Продолжаем выполнение даже если не удалось получить регистрации
			registrations[threadID] = 0
			continue
		}
		registrations[threadID] = len(registrationsResp.ThreadRegistrations)
	}

	// Формируем расширенный ответ
	enhancedThreads := make([]map[string]interface{}, 0, len(resp.Threads))
	for _, thread := range resp.Threads {
		// Get booked slots count
		bookedSlots := registrations[thread.Id]

		// Calculate available slots
		availableSlots := int(thread.MaxStudents) - bookedSlots
		if availableSlots < 0 {
			availableSlots = 0
		}

		threadData := map[string]interface{}{
			"id":              thread.Id,
			"course_id":       thread.CourseId,
			"semester_id":     thread.SemesterId,
			"teacher_id":      thread.TeacherId,
			"title":           thread.Title,
			"syllabus_url":    thread.SyllabusUrl,
			"max_students":    thread.MaxStudents,
			"booked_slots":    bookedSlots,
			"available_slots": availableSlots,
			"created_at":      thread.CreatedAt,
			"updated_at":      thread.UpdatedAt,
		}

		// Добавляем информацию о преподавателе, если доступна
		if teacher, ok := teachers[thread.TeacherId]; ok {
			threadData["teacher"] = teacher
		}

		// Добавляем информацию о курсе, если доступна
		if course, ok := courses[thread.CourseId]; ok {
			threadData["course"] = course
		}

		// Добавляем расписания, если доступны
		if threadSchedules, ok := schedules[thread.Id]; ok {
			threadData["schedules"] = threadSchedules
		} else {
			threadData["schedules"] = []*threadpb.ThreadScheduleResponse{}
		}

		enhancedThreads = append(enhancedThreads, threadData)
	}

	// Отправляем успешный ответ
	c.JSON(http.StatusOK, enhancedThreads)
}

// ListThreadsByCourseHandler получает список потоков по ID курса с информацией о преподавателях и расписаниях
func (h *ThreadHandler) ListThreadsByCourseHandler(c *gin.Context) {
	courseID := c.Param("course_id")
	if courseID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "course_id is required"})
		return
	}

	// Формируем запрос
	req := &threadpb.ThreadsByCourseRequest{
		CourseId: toInt64(courseID),
	}

	// Отправляем запрос в ThreadService
	resp, err := h.ThreadService.ListThreadsByCourse(c.Request.Context(), req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error listing threads by course: %v", err),
			"thread",
			map[string]any{"course_id": courseID, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list threads by course"})
		return
	}

	// Получаем информацию о курсе
	courseInfo, err := h.CourseService.GetCourseByID(c.Request.Context(), toInt64(courseID))
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error fetching course info: %v", err),
			"thread",
			map[string]any{"course_id": courseID, "error": err.Error()},
		)
		// Продолжаем выполнение даже если не удалось получить информацию о курсе
	}

	// Собираем уникальные ID преподавателей и потоков
	teacherIDs := make(map[int64]struct{})
	threadIDs := make([]int64, 0, len(resp.Threads))
	for _, thread := range resp.Threads {
		teacherIDs[thread.TeacherId] = struct{}{}
		threadIDs = append(threadIDs, thread.Id)
	}

	// Получаем информацию о преподавателях
	teachers := make(map[int64]*teacherDTO)
	for teacherID := range teacherIDs {
		teacher, err := h.UserService.GetUser(teacherID)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error fetching teacher info: %v", err),
				"thread",
				map[string]any{"teacher_id": teacherID, "error": err.Error()},
			)
			// Продолжаем выполнение даже если не удалось получить информацию о преподавателе
			continue
		}
		teachers[teacherID] = &teacherDTO{
			Id:      teacher.GetId(),
			Name:    teacher.GetName(),
			Surname: teacher.GetSurname(),
			Email:   teacher.GetEmail(),
		}
	}

	// Получаем расписания для всех потоков
	schedules := make(map[int64][]*threadpb.ThreadScheduleResponse)
	for _, threadID := range threadIDs {
		scheduleReq := &threadpb.ThreadSchedulesRequest{
			ThreadId: threadID,
		}
		scheduleResp, err := h.ThreadService.ListThreadSchedules(c.Request.Context(), scheduleReq)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error getting thread schedules: %v", err),
				"thread",
				map[string]any{"thread_id": threadID, "error": err.Error()},
			)
			// Продолжаем выполнение даже если не удалось получить расписания
			continue
		}
		schedules[threadID] = scheduleResp.Schedules
	}

	// Получаем информацию о регистрациях для всех потоков
	registrations := make(map[int64]int)
	for _, threadID := range threadIDs {
		registrationsResp, err := h.ThreadService.ListThreadRegistrations(c.Request.Context(), threadID)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error getting thread registrations: %v", err),
				"thread",
				map[string]any{"thread_id": threadID, "error": err.Error()},
			)
			// Продолжаем выполнение даже если не удалось получить регистрации
			registrations[threadID] = 0
			continue
		}
		registrations[threadID] = len(registrationsResp.ThreadRegistrations)
	}

	// Формируем расширенный ответ
	enhancedThreads := make([]map[string]interface{}, 0, len(resp.Threads))
	for _, thread := range resp.Threads {
		// Get booked slots count
		bookedSlots := registrations[thread.Id]

		// Calculate available slots
		availableSlots := int(thread.MaxStudents) - bookedSlots
		if availableSlots < 0 {
			availableSlots = 0
		}

		threadData := map[string]interface{}{
			"id":              thread.Id,
			"course_id":       thread.CourseId,
			"semester_id":     thread.SemesterId,
			"teacher_id":      thread.TeacherId,
			"title":           thread.Title,
			"syllabus_url":    thread.SyllabusUrl,
			"max_students":    thread.MaxStudents,
			"booked_slots":    bookedSlots,
			"available_slots": availableSlots,
			"created_at":      thread.CreatedAt,
			"updated_at":      thread.UpdatedAt,
		}

		// Добавляем информацию о преподавателе, если доступна
		if teacher, ok := teachers[thread.TeacherId]; ok {
			threadData["teacher"] = teacher
		}

		// Добавляем расписания, если доступны
		if threadSchedules, ok := schedules[thread.Id]; ok {
			threadData["schedules"] = threadSchedules
		} else {
			threadData["schedules"] = []*threadpb.ThreadScheduleResponse{}
		}

		enhancedThreads = append(enhancedThreads, threadData)
	}

	// Добавляем информацию о курсе в ответ
	response := gin.H{
		"threads": enhancedThreads,
	}

	if courseInfo != nil {
		response["course"] = gin.H{
			"id":               courseInfo.GetId(),
			"title":            courseInfo.GetTitle(),
			"description":      courseInfo.GetDescription(),
			"banner_image_url": courseInfo.GetBannerImageUrl(),
		}
	}

	// Отправляем успешный ответ
	c.JSON(http.StatusOK, response)
}

// UpdateThreadByIDHandler обновляет поток по ID
func (h *ThreadHandler) UpdateThreadByIDHandler(c *gin.Context) {
	threadID := c.Param("id")
	if threadID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "thread_id is required"})
		return
	}

	// Ограничиваем размер тела запроса до 2 МБ
	c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 2<<20) // 2 MB

	// Пытаемся распарсить multipart/form-data
	if err := c.Request.ParseMultipartForm(2 << 20); err != nil {
		if strings.Contains(err.Error(), "request body too large") {
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "file size exceeds 2MB limit"})
			return
		}
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing form data: %v", err),
			"thread",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
		return
	}

	// Извлекаем поля
	title := c.PostForm("title")
	description := c.PostForm("description")
	courseID := c.PostForm("course_id")
	semesterID := c.PostForm("semester_id")
	teacherID := c.PostForm("teacher_id")
	maxStudents := toInt32(c.PostForm("max_students"))

	// Валидация обязательных полей
	if title == "" || description == "" || courseID == "" || semesterID == "" || teacherID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "title, description, course_id, semester_id, and teacher_id are required"})
		return
	}

	var syllabusURL string

	// Пытаемся извлечь файл syllabus (опционально)
	file, header, err := c.Request.FormFile("syllabus")
	if err != nil {
		if errors.Is(err, http.ErrMissingFile) {
			// Файл не был загружен, продолжаем без него
			// syllabusURL останется пустым
		} else {
			// Другая ошибка при получении файла
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error while parsing syllabus file: %v", err),
				"thread",
				map[string]any{"title": title, "error": err.Error()},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "failed to process syllabus file"})
			return
		}
	} else {
		defer file.Close()

		// Определяем MIME-тип
		contentType := header.Header.Get("Content-Type")
		if contentType == "" {
			contentType = "application/octet-stream"
		}

		// Разрешаем только изображения и PDF (при необходимости)
		allowedTypes := []string{"image/jpeg", "image/png", "application/pdf"}
		isAllowed := false
		for _, t := range allowedTypes {
			if contentType == t {
				isAllowed = true
				break
			}
		}
		if !isAllowed {
			c.JSON(http.StatusUnsupportedMediaType, gin.H{"error": "only JPEG, PNG images and PDF files are allowed for syllabus"})
			return
		}

		// Проверяем размер файла (уже ограничен MaxBytesReader, но дополнительно проверим)
		fileSize := header.Size
		if fileSize > 2<<20 { // 2 MB
			c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "syllabus file size exceeds 2MB limit"})
			return
		}

		// Читаем содержимое файла
		fileData := make([]byte, fileSize)
		_, err := file.Read(fileData)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error reading syllabus file: %v", err),
				"thread",
				map[string]any{"title": title, "error": err.Error()},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to read syllabus file"})
			return
		}

		// Обрабатываем имя файла (заменяем пробелы и другие недопустимые символы)
		originalFilename := header.Filename
		safeFilename := strings.ReplaceAll(originalFilename, " ", "_")
		// Дополнительно можно использовать функции для более тщательной санитации имени

		objectName := fmt.Sprintf("%d_%s", time.Now().UnixNano(), safeFilename)

		// Загружаем файл в StorageService
		uploadResp, err := h.StorageService.UploadFile(
			c.Request.Context(),
			"syllabus-files", // Название bucket для syllabus
			objectName,
			contentType,
			fileData,
		)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error uploading syllabus file: %v", err),
				"thread",
				map[string]any{"title": title, "error": err.Error()},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to upload syllabus file"})
			return
		}

		// Устанавливаем URL загруженного syllabus
		syllabusURL = uploadResp.FileUrl // Предполагается, что UploadFile возвращает URL
	}

	// Формируем запрос для обновления потока
	threadUpdateReq := &threadpb.ThreadUpdateRequest{
		Id:          toInt64(threadID),
		CourseId:    toInt64(courseID),
		SemesterId:  toInt64(semesterID),
		TeacherId:   toInt64(teacherID),
		MaxStudents: maxStudents,
		Title:       title,
		SyllabusUrl: syllabusURL,
	}

	// Отправляем запрос в ThreadService
	threadResp, err := h.ThreadService.UpdateThreadByID(c.Request.Context(), threadUpdateReq)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error updating thread: %v", err),
			"thread",
			map[string]any{
				"thread_id":   threadID,
				"title":       title,
				"description": description,
				"error":       err.Error(),
			},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update thread"})
		return
	}

	// Логируем успешное обновление потока
	h.RabbitLogPublisher.PublishLog(
		"info",
		fmt.Sprintf("Thread updated successfully: %d", threadResp.Id),
		"thread",
		map[string]any{
			"thread_id":   fmt.Sprintf("%d", threadResp.Id),
			"title":       title,
			"description": description,
		},
	)

	// Отправляем успешный ответ
	c.JSON(http.StatusOK, gin.H{
		"message":      "Thread updated successfully",
		"thread_id":    threadResp.Id,
		"syllabus_url": syllabusURL,
	})
}

// GetThreadByIDHandler получает поток по ID с информацией о преподавателе, курсе и расписаниях
func (h *ThreadHandler) GetThreadByIDHandler(c *gin.Context) {
	threadID := c.Param("id")
	if threadID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "thread_id is required"})
		return
	}

	threadIDInt := toInt64(threadID)

	// Формируем запрос для получения потока
	threadReq := &threadpb.ThreadByID{
		ThreadId: threadIDInt,
	}

	// Отправляем запрос в ThreadService для получения потока
	resp, err := h.ThreadService.GetThreadByID(c.Request.Context(), threadReq)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting thread by ID: %v", err),
			"thread",
			map[string]any{"thread_id": threadID, "error": err.Error()},
		)
		c.JSON(http.StatusNotFound, gin.H{"error": "thread not found"})
		return
	}

	// Получаем информацию о преподавателе
	var teacherInfo *teacherDTO
	if resp.TeacherId > 0 {
		teacher, err := h.UserService.GetUser(resp.TeacherId)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error fetching teacher info: %v", err),
				"thread",
				map[string]any{"teacher_id": resp.TeacherId, "error": err.Error()},
			)
			// Продолжаем выполнение даже если не удалось получить информацию о преподавателе
		} else {
			teacherInfo = &teacherDTO{
				Id:      teacher.GetId(),
				Name:    teacher.GetName(),
				Surname: teacher.GetSurname(),
				Email:   teacher.GetEmail(),
			}
		}
	}

	// Получаем информацию о курсе
	var courseInfo *threadpb.CourseInfo
	if resp.CourseId > 0 {
		course, err := h.CourseService.GetCourseByID(c.Request.Context(), resp.CourseId)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error fetching course info: %v", err),
				"thread",
				map[string]any{"course_id": resp.CourseId, "error": err.Error()},
			)
			// Продолжаем выполнение даже если не удалось получить информацию о курсе
		} else {
			courseInfo = &threadpb.CourseInfo{
				Id:             course.GetId(),
				Title:          course.GetTitle(),
				Description:    course.GetDescription(),
				BannerImageUrl: course.GetBannerImageUrl(),
			}
		}
	}

	// Получаем расписания потока
	scheduleReq := &threadpb.ThreadSchedulesRequest{
		ThreadId: threadIDInt,
	}
	scheduleResp, err := h.ThreadService.ListThreadSchedules(c.Request.Context(), scheduleReq)
	var schedules []*threadpb.ThreadScheduleResponse
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting thread schedules: %v", err),
			"thread",
			map[string]any{"thread_id": threadID, "error": err.Error()},
		)
		// Продолжаем выполнение даже если не удалось получить расписания
	} else {
		schedules = scheduleResp.Schedules
	}

	// Get registrations for this thread to count booked slots
	registrationsResp, err := h.ThreadService.ListThreadRegistrations(c.Request.Context(), threadIDInt)
	var bookedSlots int
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error getting thread registrations: %v", err),
			"thread",
			map[string]any{"thread_id": threadID, "error": err.Error()},
		)
		// Continue without registration count
		bookedSlots = 0
	} else {
		bookedSlots = len(registrationsResp.ThreadRegistrations)
	}

	// Calculate available slots
	availableSlots := int(resp.MaxStudents) - bookedSlots
	if availableSlots < 0 {
		availableSlots = 0
	}

	// Формируем расширенный ответ
	response := gin.H{
		"id":              resp.Id,
		"course_id":       resp.CourseId,
		"semester_id":     resp.SemesterId,
		"teacher_id":      resp.TeacherId,
		"title":           resp.Title,
		"syllabus_url":    resp.SyllabusUrl,
		"max_students":    resp.MaxStudents,
		"booked_slots":    bookedSlots,
		"available_slots": availableSlots,
		"created_at":      resp.CreatedAt,
		"updated_at":      resp.UpdatedAt,
		"schedules":       schedules, // Добавляем расписания в ответ
	}

	// Добавляем информацию о преподавателе, если доступна
	if teacherInfo != nil {
		response["teacher"] = teacherInfo
	}

	// Добавляем информацию о курсе, если доступна
	if courseInfo != nil {
		response["course"] = courseInfo
	}

	// Отправляем успешный ответ
	c.JSON(http.StatusOK, response)
}

// DeleteThreadByIDHandler удаляет поток по ID
func (h *ThreadHandler) DeleteThreadByIDHandler(c *gin.Context) {
	threadID := c.Param("id")
	if threadID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "thread_id is required"})
		return
	}

	// Формируем запрос
	req := &threadpb.ThreadByID{
		ThreadId: toInt64(threadID),
	}

	// Отправляем запрос в ThreadService
	_, err := h.ThreadService.DeleteThreadByID(c.Request.Context(), req)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error deleting thread: %v", err),
			"thread",
			map[string]any{"thread_id": threadID, "error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete thread"})
		return
	}

	// Логируем успешное удаление
	h.RabbitLogPublisher.PublishLog(
		"info",
		fmt.Sprintf("Thread deleted successfully: %s", threadID),
		"thread",
		map[string]any{"thread_id": threadID},
	)

	// Отправляем успешный ответ
	c.JSON(http.StatusOK, gin.H{"message": "Thread deleted successfully"})
}

// Вспомогательные функции

// toInt64 конвертирует строку в int64, возвращает 0 при ошибке
func toInt64(s string) int64 {
	var i int64
	fmt.Sscanf(s, "%d", &i)
	return i
}

// RegisterUserToThreadHandler регистрирует одного пользователя в потоке
// и создаёт записи посещаемости на все будущие занятия этого потока
func (h *ThreadHandler) RegisterUserToThreadHandler(c *gin.Context) {
	var req threadpb.RegisterUserToThreadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing register user to thread request: %v", err),
			"thread_registration",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Check prerequisites before registering
	if err := h.ThreadService.CheckPrerequisites(c.Request.Context(), req.GetUserId(), req.GetThreadId()); err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Prerequisite check failed: %v", err),
			"thread_registration",
			map[string]any{
				"user_id":   req.GetUserId(),
				"thread_id": req.GetThreadId(),
				"error":     err.Error(),
			},
		)
		c.JSON(http.StatusForbidden, gin.H{"error": "prerequisites not met"})
		return
	}

	// 1) Регистрируем пользователя в потоке
	if _, err := h.ThreadService.RegisterUserToThread(c.Request.Context(), &req); err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error registering user to thread: %v", err),
			"thread_registration",
			map[string]any{
				"user_id":   req.GetUserId(),
				"thread_id": req.GetThreadId(),
				"error":     err.Error(),
			},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to register user to thread"})
		return
	}

	// 2) Генерируем посещаемость для студента на весь семестр в фоне
	go func(userID, threadID int64) {
		if err := h.seedAttendance(userID, threadID); err != nil {
			h.RabbitLogPublisher.PublishLog("error",
				fmt.Sprintf("attendance seed error for user %d: %v", userID, err),
				"attendance", map[string]any{"thread_id": threadID, "user_id": userID})
		}
	}(req.GetUserId(), req.GetThreadId())

	c.JSON(http.StatusOK, gin.H{"message": "User registered and attendance seeded successfully"})
}

// RegisterManyUsersToThreadHandler регистрирует сразу несколько пользователей
// и для каждого создаёт посещаемость на весь семестр
func (h *ThreadHandler) RegisterManyUsersToThreadHandler(c *gin.Context) {
	var req threadpb.RegisterManyUsersToThreadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.RabbitLogPublisher.PublishLog("error",
			fmt.Sprintf("parse bulk register request: %v", err),
			"thread_registration", map[string]any{"error": err.Error()})
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// 1) Регистрируем сразу всех
	if _, err := h.ThreadService.RegisterManyUsersToThread(c.Request.Context(), &req); err != nil {
		h.RabbitLogPublisher.PublishLog("error",
			fmt.Sprintf("bulk register error: %v", err),
			"thread_registration", map[string]any{"thread_id": req.GetThreadId(), "error": err.Error()})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to register users"})
		return
	}

	// 2) Для каждого пользователя запускаем сидер посещаемости
	for _, uid := range req.GetUserId() {
		go func(userID, threadID int64) {
			// seedAttendance – та же функция, что вы писали для одиночного случая,
			// просто принимает userID и threadID
			if err := h.seedAttendance(userID, threadID); err != nil {
				h.RabbitLogPublisher.PublishLog("error",
					fmt.Sprintf("attendance seed error for user %d: %v", userID, err),
					"attendance", map[string]any{"thread_id": threadID, "user_id": userID})
			}
		}(uid, req.GetThreadId())
	}

	c.JSON(http.StatusOK, gin.H{"message": "Users registered and attendance seeding started"})
}

// RemoveRegistrationToThreadHandler удаляет регистрацию одного пользователя
// и затем удаляет все его записи посещаемости для этого потока
func (h *ThreadHandler) RemoveRegistrationToThreadHandler(c *gin.Context) {
	var req threadpb.RemoveRegistrationToThreadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.RabbitLogPublisher.PublishLog("error",
			fmt.Sprintf("parse remove registration: %v", err),
			"thread_registration", map[string]any{"error": err.Error()})
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// 1) Удаляем регистрацию
	if _, err := h.ThreadService.RemoveRegistrationToThread(c.Request.Context(), &req); err != nil {
		h.RabbitLogPublisher.PublishLog("error",
			fmt.Sprintf("remove registration error: %v", err),
			"thread_registration",
			map[string]any{"user_id": req.GetUserId(), "thread_id": req.GetThreadId(), "error": err.Error()})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to remove registration"})
		return
	}

	// 2) Фоновая очистка посещаемости
	go func(userID, threadID int64) {
		if err := h.AttendanceService.DeleteAttendancesByThreadUser(context.Background(), threadID, userID); err != nil {
			h.RabbitLogPublisher.PublishLog("error",
				fmt.Sprintf("attendance cleanup error for user %d: %v", userID, err),
				"attendance", map[string]any{"thread_id": threadID, "user_id": userID})
		}
	}(req.GetUserId(), req.GetThreadId())

	c.JSON(http.StatusOK, gin.H{"message": "Registration removed and attendance cleanup started"})
}

// RemoveManyRegistrationsToThreadHandler удаляет сразу нескольких пользователей
// и очищает их посещаемость по аналогии
func (h *ThreadHandler) RemoveManyRegistrationsToThreadHandler(c *gin.Context) {
	var req threadpb.RemoveManyRegistrationsToThreadRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.RabbitLogPublisher.PublishLog("error",
			fmt.Sprintf("parse bulk remove request: %v", err),
			"thread_registration", map[string]any{"error": err.Error()})
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// 1) Удаляем все регистрации
	if _, err := h.ThreadService.RemoveManyRegistrationsToThread(c.Request.Context(), &req); err != nil {
		h.RabbitLogPublisher.PublishLog("error",
			fmt.Sprintf("bulk remove error: %v", err),
			"thread_registration", map[string]any{"thread_id": req.GetThreadId(), "error": err.Error()})
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to remove registrations"})
		return
	}

	// 2) Очищаем посещаемость для каждого пользователя
	for _, uid := range req.GetUserId() {
		go func(userID, threadID int64) {
			if err := h.AttendanceService.DeleteAttendancesByThreadUser(context.Background(), threadID, userID); err != nil {
				h.RabbitLogPublisher.PublishLog("error",
					fmt.Sprintf("attendance cleanup error for user %d: %v", userID, err),
					"attendance", map[string]any{"thread_id": threadID, "user_id": userID})
			}
		}(uid, req.GetThreadId())
	}

	c.JSON(http.StatusOK, gin.H{"message": "Registrations removed and attendance cleanup started"})
}

func toInt32(s string) int32 {
	i, _ := strconv.ParseInt(s, 10, 32)
	return int32(i)
}

// convertNumericToLetterGrade converts a numeric grade (0-100) to a letter grade
func convertNumericToLetterGrade(numericGrade float64) string {
	switch {
	case numericGrade >= 95:
		return "A+"
	case numericGrade >= 90:
		return "A"
	case numericGrade >= 85:
		return "A-"
	case numericGrade >= 80:
		return "B+"
	case numericGrade >= 75:
		return "B"
	case numericGrade >= 70:
		return "B-"
	case numericGrade >= 65:
		return "C+"
	case numericGrade >= 60:
		return "C"
	case numericGrade >= 55:
		return "C-"
	case numericGrade >= 50:
		return "D"
	default:
		return "F"
	}
}

// convertNumericToGradePoints converts a numeric grade (0-100) to grade points (0.0-4.0)
func convertNumericToGradePoints(numericGrade float64) float64 {
	switch {
	case numericGrade >= 95:
		return 4.0
	case numericGrade >= 90:
		return 3.7
	case numericGrade >= 85:
		return 3.3
	case numericGrade >= 80:
		return 3.0
	case numericGrade >= 75:
		return 2.7
	case numericGrade >= 70:
		return 2.3
	case numericGrade >= 65:
		return 2.0
	case numericGrade >= 60:
		return 1.7
	case numericGrade >= 55:
		return 1.3
	case numericGrade >= 50:
		return 1.0
	default:
		return 0.0
	}
}

// GradeFinalThreadRegistrationRequest represents the request for grading final thread registration
type GradeFinalThreadRegistrationRequest struct {
	UserID     int64   `json:"user_id" binding:"required,min=1"`
	ThreadID   int64   `json:"thread_id" binding:"required,min=1"`
	FinalGrade float64 `json:"final_grade" binding:"required,min=0,max=100"`
}

// GradeFinalThreadRegistrationHandler handles POST /thread/grade-final
func (h *ThreadHandler) GradeFinalThreadRegistrationHandler(c *gin.Context) {
	var req GradeFinalThreadRegistrationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Call the thread service to update final grade
	resp, err := h.ThreadService.GradeFinalThreadRegistration(c.Request.Context(), &threadpb.GradeFinalThreadRegistrationRequest{
		UserId:     req.UserID,
		ThreadId:   req.ThreadID,
		FinalGrade: req.FinalGrade,
	})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// Automatically create transcript entry
	go func() {
		// Get thread information to extract course and semester
		threadResp, err := h.ThreadService.GetThreadByID(context.Background(), &threadpb.ThreadByID{ThreadId: req.ThreadID})
		if err != nil {
			h.RabbitLogPublisher.PublishLog("error",
				fmt.Sprintf("Failed to get thread info for transcript entry: %v", err),
				"transcript", map[string]any{"thread_id": req.ThreadID, "user_id": req.UserID})
			return
		}

		// Convert numeric grade to letter grade and grade points
		letterGrade := convertNumericToLetterGrade(req.FinalGrade)
		gradePoints := convertNumericToGradePoints(req.FinalGrade)

		// Create transcript entry request
		transcriptReq := &transcriptpb.AddTranscriptEntryRequest{
			TranscriptId:   1, // TODO: Get actual transcript ID for the user
			CourseId:       threadResp.CourseId,
			ThreadId:       req.ThreadID,
			SemesterId:     threadResp.SemesterId,
			GradeLetter:    letterGrade,
			GradeNumeric:   req.FinalGrade,
			GradePoints:    gradePoints,
			Credits:        5, // Always set to 5 as per user requirement
			IsTransfer:     false,
			IsRepeated:     false,
			CompletionDate: nil, // Will be set to current time by the service
		}

		// Call transcript service to add entry
		_, err = h.TranscriptService.AddTranscriptEntry(context.Background(), transcriptReq)
		if err != nil {
			h.RabbitLogPublisher.PublishLog("error",
				fmt.Sprintf("Failed to create transcript entry: %v", err),
				"transcript", map[string]any{"thread_id": req.ThreadID, "user_id": req.UserID})
		} else {
			h.RabbitLogPublisher.PublishLog("info",
				fmt.Sprintf("Transcript entry created successfully for user %d, thread %d", req.UserID, req.ThreadID),
				"transcript", map[string]any{"thread_id": req.ThreadID, "user_id": req.UserID})
		}
	}()

	c.JSON(http.StatusOK, gin.H{
		"message":      resp.Message,
		"registration": resp.Registration,
	})
}

// seedAttendance генерирует записи посещаемости для указанного пользователя и потока на весь семестр
func (h *ThreadHandler) seedAttendance(userID, threadID int64) error {
	ctx := context.Background()

	// 1) Узнаём, к какому семестру привязан поток
	thr, err := h.ThreadService.GetThreadByID(ctx, &threadpb.ThreadByID{ThreadId: threadID})
	if err != nil {
		return fmt.Errorf("fetch thread: %w", err)
	}
	semID := thr.GetSemesterId()

	// 2) Загружаем дату начала и конца семестра
	sem, err := h.SemesterService.GetSemesterByID(ctx, &semesterpb.SemesterByID{Id: semID})
	if err != nil {
		return fmt.Errorf("fetch semester: %w", err)
	}
	start := sem.GetStartDate().AsTime().Truncate(24 * time.Hour)
	end := sem.GetEndDate().AsTime().Truncate(24 * time.Hour)

	// 3) Получаем даты каникул
	breaksResp, err := h.SemesterService.ListSemesterBreaks(ctx, &semesterpb.SemesterByID{Id: semID})
	if err != nil {
		return fmt.Errorf("fetch breaks: %w", err)
	}
	breakSet := make(map[time.Time]struct{}, len(breaksResp.SemesterBreaks))
	for _, b := range breaksResp.SemesterBreaks {
		d := b.GetBreakDate().AsTime().Truncate(24 * time.Hour)
		breakSet[d] = struct{}{}
	}

	// 4) Получаем шаблон расписания потока
	schedResp, err := h.ThreadService.ListThreadSchedules(ctx, &threadpb.ThreadSchedulesRequest{ThreadId: threadID})
	if err != nil {
		return fmt.Errorf("fetch schedules: %w", err)
	}

	// 5) Проходим по всем датам семестра
	for d := start; !d.After(end); d = d.AddDate(0, 0, 1) {
		// пропускаем каникулы
		if _, isBreak := breakSet[d]; isBreak {
			continue
		}
		// для каждой записи в шаблоне
		for _, sch := range schedResp.Schedules {
			// Date.Weekday() даёт 0=Sunday…6=Saturday, а мы используем 1=Mon…7=Sun
			if int(d.Weekday())%7+1 != int(sch.DayOfWeek) {
				continue
			}
			// создаём запись посещаемости со статусом unmarked
			_, err := h.AttendanceService.CreateAttendance(ctx, clients.CreateAttendanceParams{
				ThreadID:       threadID,
				UserID:         userID,
				AttendanceDate: d,
				Status:         attendancepb.AttendanceStatus_ATTENDANCE_STATUS_UNMARKED,
			})
			if err != nil {
				// логируем, но продолжаем сеять дальше
				h.RabbitLogPublisher.PublishLog("error",
					fmt.Sprintf("attendance seed error: %v", err),
					"attendance",
					map[string]any{"thread_id": threadID, "user_id": userID, "date": d.Format("2006-01-02")},
				)
			}
		}
	}

	return nil
}

// ListThreadRegistrationsHandler возвращает список всех регистраций
func (h *ThreadHandler) ListThreadRegistrationsHandler(c *gin.Context) {

	resp, err := h.ThreadService.ListThreadRegistrations(c.Request.Context(), 0)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error listing thread registrations: %v", err),
			"thread_registration",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to list thread registrations"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"registrations": resp.ThreadRegistrations})
}

// ListThreadsForUserHandler возвращает все потоки (детальной информацией),
// на которые записан пользователь.
func (h *ThreadHandler) ListThreadsForUserHandler(c *gin.Context) {
	// ── validate ──────────────────────────────────────────
	userID, err := strconv.ParseInt(c.Param("user_id"), 10, 64)
	if err != nil || userID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid user_id"})
		return
	}

	// ── fetch threads from Thread‑service ────────────────
	trResp, err := h.ThreadService.ListThreadsForUser(
		c.Request.Context(), &threadpb.UserThreadsRequest{UserId: userID},
	)
	if err != nil {
		mapGRPCError(c, h.RabbitLogPublisher,
			"thread_user_list",
			fmt.Sprintf("ListThreadsForUser: %v", err),
			map[string]any{"user_id": userID}, err)
		return
	}
	threadsPB := trResp.GetThreads()
	if len(threadsPB) == 0 {
		c.JSON(http.StatusOK, gin.H{"threads": []any{}})
		return
	}

	// ── teacher map exactly as раньше ─────────────────────
	teacherSet := map[int64]struct{}{}
	for _, t := range threadsPB {
		if id := t.Thread.TeacherId; id > 0 {
			teacherSet[id] = struct{}{}
		}
	}
	teachers := map[int64]teacherDTO{}
	for id := range teacherSet {
		u, err := h.UserService.GetUser(id)
		if err != nil {
			h.RabbitLogPublisher.PublishLog("error",
				fmt.Sprintf("GetUser(%d): %v", id, err),
				"user_fetch", map[string]any{"teacher_id": id})
			fmt.Println(fmt.Sprintf("GetUser(%d): %v", id, err))
			continue
		}
		teachers[id] = teacherDTO{
			Id:      u.GetId(),
			Name:    u.GetName(),
			Surname: u.GetSurname(),
			Email:   u.GetEmail(),
		}
	}

	// ── формируем HTTP‑DTO с teacher ──────────────────────
	out := make([]threadHTTP, 0, len(threadsPB))
	for _, t := range threadsPB {
		dto := threadHTTP{ThreadWithDetails: t}
		if tch, ok := teachers[t.Thread.TeacherId]; ok {
			dto.Teacher = &tch
		}
		out = append(out, dto)
	}

	c.JSON(http.StatusOK, gin.H{"threads": out})
}

// mapGRPCError – единообразный маппинг gRPC‑ошибок в HTTP + лог
func mapGRPCError(c *gin.Context, log clients.LogPublisher,
	event string, msg string, fields map[string]any, err error) {

	httpStatus := http.StatusInternalServerError
	if st, ok := status.FromError(err); ok {
		switch st.Code() {
		case codes.InvalidArgument:
			httpStatus = http.StatusBadRequest
		case codes.NotFound:
			httpStatus = http.StatusNotFound
		}
	}

	_ = log.PublishLog("error", msg, event, fields)
	c.JSON(httpStatus, gin.H{"error": stErrorMessage(err)})
}

func stErrorMessage(err error) string {
	if st, ok := status.FromError(err); ok {
		return st.Message()
	}
	return "internal error"
}

func (h *ThreadHandler) ListWeeksWithHomeworkHandler(c *gin.Context) {
	// ────────────────── input / validation ──────────────────
	threadID, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil || threadID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid thread_id"})
		return
	}
	userIDStr := c.Query("user_id")
	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil || userID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid user_id"})
		return
	}

	// ─────────────────── gRPC call ──────────────────────────
	resp, err := h.ThreadService.ListWeeksWithHomework(
		c.Request.Context(),
		&threadpb.WeeksWithHwRequest{
			ThreadId: threadID,
			UserId:   userID,
		},
	)
	if err != nil {
		mapGRPCError(
			c,
			h.RabbitLogPublisher,
			"weeks_hw",
			fmt.Sprintf("ListWeeksWithHomework: %v", err),
			map[string]any{
				"thread_id": threadID,
				"user_id":   userID,
			},
			err,
		)
		return
	}

	// ─────────────────── success ────────────────────────────
	c.JSON(http.StatusOK, gin.H{
		"thread_id": threadID,
		"user_id":   userID,
		"weeks":     resp.GetWeeks(), // []*threadpb.WeekWithHw
	})
}

// ListThreadGroupmatesHandler returns a list of all students in a thread,
// enriched with their full info from User-service.
func (h *ThreadHandler) ListThreadGroupmatesHandler(c *gin.Context) {
	// 1) validate thread_id
	threadID, err := strconv.ParseInt(c.Param("thread_id"), 10, 64)
	if err != nil || threadID <= 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid thread_id"})
		return
	}

	// 2) fetch registrations from Thread-service
	listResp, err := h.ThreadService.ListThreadRegistrations(
		c.Request.Context(), threadID,
	)
	if err != nil {
		mapGRPCError(
			c, h.RabbitLogPublisher,
			"thread_registrations",
			fmt.Sprintf("ListThreadRegistrations: %v", err),
			map[string]any{"thread_id": threadID},
			err,
		)
		return
	}

	// 3) iterate over registrations, call User-service
	var groupmates []gin.H
	for _, reg := range listResp.GetThreadRegistrations() {
		uid := reg.GetUserId()
		userResp, err := h.UserService.GetUser(uid)
		if err != nil {
			// логируем, но не прерываем
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("GetUser(%d): %v", uid, err),
				"thread_groupmates",
				map[string]any{"user_id": uid},
			)
			continue
		}
		groupmates = append(groupmates, gin.H{
			"id":      userResp.GetId(),
			"name":    userResp.GetName(),
			"surname": userResp.GetSurname(),
			"email":   userResp.GetEmail(),
			"role":    userResp.GetRole(),
		})
	}

	// 4) вернуть результат
	c.JSON(http.StatusOK, gin.H{
		"thread_id": threadID,
		"members":   groupmates,
	})
}

// Schedule represents a thread schedule
type Schedule struct {
	DayOfWeek  int    `json:"day_of_week"`
	StartTime  string `json:"start_time"`
	EndTime    string `json:"end_time"`
	Location   string `json:"location,omitempty"`    // Kept for backward compatibility
	LocationID int64  `json:"location_id,omitempty"` // New field for location ID
}

// ThreadAvailabilityRequest represents a request to get thread availability information
type ThreadAvailabilityRequest struct {
	ThreadIDs []int64 `json:"thread_ids" binding:"required"`
}

// ThreadAvailabilityResponse represents information about thread availability
type ThreadAvailabilityResponse struct {
	ID             int64 `json:"id"`
	MaxStudents    int32 `json:"max_students"`
	BookedSlots    int   `json:"booked_slots"`
	AvailableSlots int   `json:"available_slots"`
}

// GetThreadAvailabilityHandler returns availability information for multiple threads
func (h *ThreadHandler) GetThreadAvailabilityHandler(c *gin.Context) {
	// Parse request body
	var req ThreadAvailabilityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error parsing thread availability request: %v", err),
			"thread_availability",
			map[string]any{"error": err.Error()},
		)
		c.JSON(http.StatusBadRequest, gin.H{"error": "invalid request body"})
		return
	}

	// Validate request
	if len(req.ThreadIDs) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "thread_ids is required and must not be empty"})
		return
	}

	// Get thread information and count registrations for each thread
	threadAvailability := make([]ThreadAvailabilityResponse, 0, len(req.ThreadIDs))
	for _, threadID := range req.ThreadIDs {
		// Get thread information
		threadReq := &threadpb.ThreadByID{
			ThreadId: threadID,
		}
		threadResp, err := h.ThreadService.GetThreadByID(c.Request.Context(), threadReq)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error getting thread by ID: %v", err),
				"thread_availability",
				map[string]any{"thread_id": threadID, "error": err.Error()},
			)
			// Skip this thread and continue with others
			continue
		}

		// Get registrations for this thread
		registrationsResp, err := h.ThreadService.ListThreadRegistrations(c.Request.Context(), threadID)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error getting thread registrations: %v", err),
				"thread_availability",
				map[string]any{"thread_id": threadID, "error": err.Error()},
			)
			// Use 0 as the default number of registrations
			threadAvailability = append(threadAvailability, ThreadAvailabilityResponse{
				ID:             threadID,
				MaxStudents:    threadResp.MaxStudents,
				BookedSlots:    0,
				AvailableSlots: int(threadResp.MaxStudents),
			})
			continue
		}

		// Calculate availability
		bookedSlots := len(registrationsResp.ThreadRegistrations)
		availableSlots := int(threadResp.MaxStudents) - bookedSlots
		if availableSlots < 0 {
			availableSlots = 0
		}

		// Add to response
		threadAvailability = append(threadAvailability, ThreadAvailabilityResponse{
			ID:             threadID,
			MaxStudents:    threadResp.MaxStudents,
			BookedSlots:    bookedSlots,
			AvailableSlots: availableSlots,
		})
	}

	// Return response
	c.JSON(http.StatusOK, gin.H{
		"thread_availability": threadAvailability,
	})
}

// ThreadWithScheduleRequest represents a request to create a thread with schedules
type ThreadWithScheduleRequest struct {
	Title       string      `json:"title"`
	Description string      `json:"description,omitempty"`
	CourseID    json.Number `json:"course_id"`
	SemesterID  json.Number `json:"semester_id"`
	TeacherID   json.Number `json:"teacher_id"`
	MaxStudents int32       `json:"max_students"`
	Schedules   []Schedule  `json:"schedules"`
}

// CreateThreadWithScheduleHandler creates a new thread with schedules in one request
func (h *ThreadHandler) CreateThreadWithScheduleHandler(c *gin.Context) {
	// Check content type to determine how to parse the request
	contentType := c.GetHeader("Content-Type")

	var (
		title       string
		description string
		courseID    string
		semesterID  string
		teacherID   string
		maxStudents int32
		schedules   []Schedule
	)

	// Handle JSON request
	if strings.Contains(contentType, "application/json") {
		// Parse JSON request
		var req ThreadWithScheduleRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error parsing JSON request: %v", err),
				"thread_with_schedule",
				map[string]any{"error": err.Error()},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse JSON request"})
			return
		}

		// Extract fields from JSON request
		title = req.Title
		description = req.Description
		courseID = req.CourseID.String()
		semesterID = req.SemesterID.String()
		teacherID = req.TeacherID.String()
		maxStudents = req.MaxStudents
		schedules = req.Schedules
	} else {
		// Handle multipart form data
		// Limit request body size
		c.Request.Body = http.MaxBytesReader(c.Writer, c.Request.Body, 2<<20) // 2 MB

		// Parse multipart form data
		if err := c.Request.ParseMultipartForm(2 << 20); err != nil {
			if strings.Contains(err.Error(), "request body too large") {
				c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "file size exceeds 2MB limit"})
				return
			}
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error parsing form data: %v", err),
				"thread_with_schedule",
				map[string]any{"error": err.Error()},
			)
			c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse form data"})
			return
		}

		// Extract thread fields
		title = c.PostForm("title")
		description = c.PostForm("description")
		courseID = c.PostForm("course_id")
		semesterID = c.PostForm("semester_id")
		teacherID = c.PostForm("teacher_id")
		maxStudents = toInt32(c.PostForm("max_students"))

		// Check if we have an array of schedules
		schedulesJSON := c.PostForm("schedules")
		if schedulesJSON != "" {
			// Parse JSON array of schedules
			if err := json.Unmarshal([]byte(schedulesJSON), &schedules); err != nil {
				h.RabbitLogPublisher.PublishLog(
					"error",
					fmt.Sprintf("Error parsing schedules JSON: %v", err),
					"thread_with_schedule",
					map[string]any{"error": err.Error()},
				)
				c.JSON(http.StatusBadRequest, gin.H{"error": "failed to parse schedules JSON"})
				return
			}
		} else if c.PostForm("schedules.0.day_of_week") != "" {
			// Multiple schedules format with indexed keys
			index := 0
			for {
				dayKey := fmt.Sprintf("schedules.%d.day_of_week", index)
				startKey := fmt.Sprintf("schedules.%d.start_time", index)
				endKey := fmt.Sprintf("schedules.%d.end_time", index)
				locKey := fmt.Sprintf("schedules.%d.location", index)
				locIDKey := fmt.Sprintf("schedules.%d.location_id", index)

				dayStr := c.PostForm(dayKey)
				if dayStr == "" {
					break // No more schedules
				}

				day, err := strconv.Atoi(dayStr)
				if err != nil {
					c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid day_of_week value for schedule %d", index)})
					return
				}

				// Parse location_id if provided
				var locationID int64
				locIDStr := c.PostForm(locIDKey)
				if locIDStr != "" {
					locationID, err = strconv.ParseInt(locIDStr, 10, 64)
					if err != nil {
						c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("invalid location_id value for schedule %d", index)})
						return
					}
				}

				schedules = append(schedules, Schedule{
					DayOfWeek:  day,
					StartTime:  c.PostForm(startKey),
					EndTime:    c.PostForm(endKey),
					Location:   c.PostForm(locKey),
					LocationID: locationID,
				})

				index++
			}
		} else {
			// Single schedule format (backward compatibility)
			dayStr := c.PostForm("day_of_week")
			if dayStr != "" {
				day, err := strconv.Atoi(dayStr)
				if err != nil {
					c.JSON(http.StatusBadRequest, gin.H{"error": "invalid day_of_week value"})
					return
				}

				// Parse location_id if provided
				var locationID int64
				locIDStr := c.PostForm("location_id")
				if locIDStr != "" {
					locationID, err = strconv.ParseInt(locIDStr, 10, 64)
					if err != nil {
						c.JSON(http.StatusBadRequest, gin.H{"error": "invalid location_id value"})
						return
					}
				}

				schedules = append(schedules, Schedule{
					DayOfWeek:  day,
					StartTime:  c.PostForm("start_time"),
					EndTime:    c.PostForm("end_time"),
					Location:   c.PostForm("location"),
					LocationID: locationID,
				})
			}
		}
	}

	// Validate required fields
	if title == "" || courseID == "" || semesterID == "" || teacherID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "title, course_id, semester_id, and teacher_id are required"})
		return
	}

	if len(schedules) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "at least one schedule with day_of_week, start_time, and end_time is required"})
		return
	}

	// Validate each schedule
	for i, schedule := range schedules {
		if schedule.DayOfWeek < 1 || schedule.DayOfWeek > 7 || schedule.StartTime == "" || schedule.EndTime == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("schedule %d is invalid or missing required fields", i)})
			return
		}
	}

	// Process syllabus file if provided
	var syllabusURL string

	// Only try to get the file if we're not using JSON
	if !strings.Contains(contentType, "application/json") {
		file, header, err := c.Request.FormFile("syllabus")
		if err != nil {
			if !errors.Is(err, http.ErrMissingFile) {
				h.RabbitLogPublisher.PublishLog(
					"error",
					fmt.Sprintf("Error while parsing syllabus file: %v", err),
					"thread_with_schedule",
					map[string]any{"title": title, "error": err.Error()},
				)
				c.JSON(http.StatusBadRequest, gin.H{"error": "failed to process syllabus file"})
				return
			}
			// No file uploaded, continue without it
		} else {
			defer file.Close()

			// Determine MIME type
			fileContentType := header.Header.Get("Content-Type")
			if fileContentType == "" {
				fileContentType = "application/octet-stream"
			}

			// Allow only specific file types
			allowedTypes := []string{"image/jpeg", "image/png", "application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}
			isAllowed := false
			for _, t := range allowedTypes {
				if fileContentType == t {
					isAllowed = true
					break
				}
			}
			if !isAllowed {
				c.JSON(http.StatusUnsupportedMediaType, gin.H{"error": "only JPEG, PNG images and PDF, DOCX files are allowed for syllabus"})
				return
			}

			// Check file size
			fileSize := header.Size
			if fileSize > 2<<20 { // 2 MB
				c.JSON(http.StatusRequestEntityTooLarge, gin.H{"error": "syllabus file size exceeds 2MB limit"})
				return
			}

			// Read file content
			fileData := make([]byte, fileSize)
			_, err := file.Read(fileData)
			if err != nil {
				h.RabbitLogPublisher.PublishLog(
					"error",
					fmt.Sprintf("Error reading syllabus file: %v", err),
					"thread_with_schedule",
					map[string]any{"title": title, "error": err.Error()},
				)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to read syllabus file"})
				return
			}

			// Process filename
			originalFilename := header.Filename
			safeFilename := strings.ReplaceAll(originalFilename, " ", "_")
			objectName := fmt.Sprintf("%d_%s", time.Now().UnixNano(), safeFilename)

			// Upload file to storage
			_, err = h.StorageService.UploadFile(
				c.Request.Context(),
				"syllabus-files",
				objectName,
				fileContentType,
				fileData,
			)
			if err != nil {
				h.RabbitLogPublisher.PublishLog(
					"error",
					fmt.Sprintf("Error uploading syllabus file: %v", err),
					"thread_with_schedule",
					map[string]any{"title": title, "error": err.Error()},
				)
				c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to upload syllabus file"})
				return
			}

			syllabusURL = fmt.Sprintf("syllabus-files/%s", objectName)
		}
	}

	// Step 1: Create the thread
	// Note: The ThreadRequest protobuf doesn't have a Description field
	// We'll store the description in the title field with a separator
	threadTitle := title
	if description != "" {
		// We'll log the description for future reference
		h.RabbitLogPublisher.PublishLog(
			"info",
			fmt.Sprintf("Thread description: %s", description),
			"thread_with_schedule",
			map[string]any{
				"title":       title,
				"description": description,
			},
		)
	}

	threadReq := &threadpb.ThreadRequest{
		CourseId:    toInt64(courseID),
		SemesterId:  toInt64(semesterID),
		TeacherId:   toInt64(teacherID),
		MaxStudents: maxStudents,
		Title:       threadTitle,
		SyllabusUrl: syllabusURL,
	}

	threadResp, err := h.ThreadService.CreateThread(c.Request.Context(), threadReq)
	if err != nil {
		h.RabbitLogPublisher.PublishLog(
			"error",
			fmt.Sprintf("Error creating thread: %v", err),
			"thread_with_schedule",
			map[string]any{
				"title":     title,
				"course_id": courseID,
				"error":     err.Error(),
			},
		)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to create thread"})
		return
	}

	// Step 2: Create schedules for the thread
	var scheduleIDs []int64

	for i, schedule := range schedules {
		// Create schedule request with location
		scheduleReq := &threadpb.ThreadScheduleRequest{
			ThreadId:  threadResp.Id,
			DayOfWeek: uint32(schedule.DayOfWeek),
			StartTime: schedule.StartTime,
			EndTime:   schedule.EndTime,
		}

		// Use location_id if provided, otherwise use location name
		if schedule.LocationID > 0 {
			// Get location by ID to set the location name
			locationResp, err := h.ThreadService.GetLocationByID(c.Request.Context(), schedule.LocationID)
			if err != nil {
				h.RabbitLogPublisher.PublishLog(
					"error",
					fmt.Sprintf("Error getting location by ID: %v", err),
					"thread_with_schedule",
					map[string]any{"location_id": schedule.LocationID, "error": err.Error()},
				)
				// Continue with empty location if location not found
			} else {
				// Set the location name from the location object
				scheduleReq.Location = locationResp.Name
				// The location_id is stored in the database through the location name
			}
		} else if schedule.Location != "" {
			scheduleReq.Location = schedule.Location
		}

		// Create schedule
		scheduleResp, err := h.ThreadService.CreateThreadSchedule(c.Request.Context(), scheduleReq)
		if err != nil {
			h.RabbitLogPublisher.PublishLog(
				"error",
				fmt.Sprintf("Error creating thread schedule: %v", err),
				"thread_with_schedule",
				map[string]any{"thread_id": threadResp.Id, "error": err.Error()},
			)
			c.JSON(http.StatusInternalServerError, gin.H{"error": fmt.Sprintf("thread created but failed to create schedule %d", i)})
			return
		}

		// Add schedule ID to list
		scheduleIDs = append(scheduleIDs, scheduleResp.Id)
	}

	// Log successful creation
	h.RabbitLogPublisher.PublishLog(
		"info",
		fmt.Sprintf("Thread with %d schedules created successfully: thread_id=%d", len(scheduleIDs), threadResp.Id),
		"thread_with_schedule",
		map[string]any{
			"thread_id":    threadResp.Id,
			"schedule_ids": scheduleIDs,
		},
	)

	// Return success response
	c.JSON(http.StatusCreated, gin.H{
		"message":      "Thread with schedules created successfully",
		"thread_id":    threadResp.Id,
		"schedule_ids": scheduleIDs,
		"syllabus_url": syllabusURL,
	})
}
