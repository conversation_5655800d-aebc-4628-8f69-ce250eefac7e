syntax = "proto3";

package threadpb;

import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "pb/assignment/assignment.proto";

option go_package = "github.com/olzzhas/edunite-server/course_service/pb/thread;threadpb";

// ========================================================
//  SERVICE
// ========================================================
service ThreadService {
  // -------- Thread ------------------------------------------------------
  rpc CreateThread         (ThreadRequest)          returns (ThreadResponse)          {}
  rpc ListThreadsByCourse  (ThreadsByCourseRequest) returns (ThreadsResponse)         {}
  rpc ListThreads          (ThreadEmptyRequest)     returns (ThreadsResponse)         {}
  rpc GetThreadByID        (ThreadByID)             returns (ThreadResponse)          {}
  rpc UpdateThreadByID     (ThreadUpdateRequest)    returns (ThreadResponse)          {}
  rpc DeleteThreadByID     (ThreadByID)             returns (ThreadEmptyResponse)     {}

  // -------- Registrations ----------------------------------------------
  rpc RegisterUserToThread            (RegisterUserToThreadRequest)            returns (ThreadEmptyResponse) {}
  rpc RegisterManyUsersToThread       (RegisterManyUsersToThreadRequest)       returns (ThreadEmptyResponse) {}
  rpc RemoveRegistrationToThread      (RemoveRegistrationToThreadRequest)      returns (ThreadEmptyResponse) {}
  rpc RemoveManyRegistrationsToThread (RemoveManyRegistrationsToThreadRequest) returns (ThreadEmptyResponse) {}
  rpc ListThreadRegistrations         (ThreadByID)                     returns (ListThreadRegistrationsResponse) {}
  rpc GradeFinalThreadRegistration    (GradeFinalThreadRegistrationRequest)    returns (GradeFinalThreadRegistrationResponse) {}

  // -------- Week --------------------------------------------------------
  rpc CreateWeek        (WeekRequest)        returns (WeekResponse)        {}
  rpc ListWeeksForThread(WeeksForThreadRequest) returns (WeeksResponse)     {}
  rpc GetWeekByID       (WeekByID)           returns (WeekResponse)        {}
  rpc UpdateWeek        (WeekUpdateRequest)  returns (WeekResponse)        {}
  rpc DeleteWeekByID    (WeekByID)           returns (ThreadEmptyResponse) {}

  // -------- Schedule ----------------------------------------------------
  rpc CreateThreadSchedule (ThreadScheduleRequest)       returns (ThreadScheduleResponse) {}
  rpc ListThreadSchedules  (ThreadSchedulesRequest)      returns (ThreadSchedulesResponse) {}
  rpc GetThreadScheduleByID(ThreadScheduleByID)          returns (ThreadScheduleResponse) {}
  rpc UpdateThreadSchedule (ThreadScheduleUpdateRequest) returns (ThreadScheduleResponse) {}
  rpc DeleteThreadScheduleByID(ThreadScheduleByID)       returns (ThreadEmptyResponse)     {}

  // -------- Location -----------------------------------------------------
  rpc CreateLocation      (LocationRequest)              returns (LocationResponse)        {}
  rpc GetLocationByID     (LocationByID)                 returns (LocationResponse)        {}
  rpc ListLocations       (LocationEmptyRequest)         returns (LocationsResponse)       {}
  rpc UpdateLocation      (LocationUpdateRequest)        returns (LocationResponse)        {}
  rpc DeleteLocation      (LocationByID)                 returns (ThreadEmptyResponse)     {}
  rpc CheckLocationAvailability(LocationAvailabilityRequest) returns (LocationAvailabilityResponse) {}

  // -------- For user ----------------------------------------------------
  rpc ListThreadsForUser  (UserThreadsRequest)           returns (UserThreadsResponse)     {}
  rpc ListWeeksWithHomework(WeeksWithHwRequest) returns (WeeksWithHwResponse) {};

  // -------- Prerequisites ------------------------------------------------
  rpc CheckPrerequisites(CheckPrerequisitesRequest) returns (google.protobuf.Empty);
}

// ========================================================
//  THREAD core
// ========================================================
message ThreadRequest {
  int64  course_id     = 1;
  int64  semester_id   = 2;
  int64  teacher_id    = 3;
  string title         = 4;
  string syllabus_url  = 5;
  int32  max_students  = 6;
}
message ThreadUpdateRequest {
  int64  id            = 1;
  int64  course_id     = 2;
  int64  semester_id   = 3;
  int64  teacher_id    = 4;
  string title         = 5;
  string syllabus_url  = 6;
  int32  max_students  = 7;
}
message ThreadResponse {
  int64  id            = 1;
  int64  course_id     = 2;
  int64  semester_id   = 3;
  int64  teacher_id    = 4;
  string title         = 5;
  string syllabus_url  = 6;
  int32  max_students  = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
}
message ThreadByID                { int64 thread_id = 1; }
message ThreadsByCourseRequest    { int64 course_id = 1; }
message ThreadsResponse           { repeated ThreadResponse threads = 1; }
message ThreadEmptyRequest        {}
message ThreadEmptyResponse       {}

// ========================================================
//  REGISTRATIONS
// ========================================================
message RegisterUserToThreadRequest            { int64 user_id = 1; int64 thread_id = 2; }
message RegisterManyUsersToThreadRequest       { int64 thread_id = 1; repeated int64 user_id = 2; }
message RemoveRegistrationToThreadRequest      { int64 user_id = 1; int64 thread_id = 2; }
message RemoveManyRegistrationsToThreadRequest { int64 thread_id = 1; repeated int64 user_id = 2; }

message ThreadRegistrationResponse { int64 user_id = 1; int64 thread_id = 2; float final_grade = 3; }
message ListThreadRegistrationsResponse { repeated ThreadRegistrationResponse thread_registrations = 1; }

// Final grading for thread registration
message GradeFinalThreadRegistrationRequest {
    int64 user_id = 1;
    int64 thread_id = 2;
    double final_grade = 3; // 0-100 numeric grade
}

message GradeFinalThreadRegistrationResponse {
    string message = 1;
    ThreadRegistrationResponse registration = 2;
}

// ========================================================
//  WEEK
// ========================================================
message WeekRequest {
  int64  thread_id    = 1;
  uint32 week_number  = 2;
  string type         = 3;
  string title        = 4;
  string description  = 5;
}
message WeekUpdateRequest {
  int64  week_id      = 1;
  int64  thread_id    = 2;
  uint32 week_number  = 3;
  string type         = 4;
  string title        = 5;
  string description  = 6;
}
message WeekResponse {
  int64  id           = 1;
  int64  thread_id    = 2;
  uint32 week_number  = 3;
  string type         = 4;
  string title        = 5;
  string description  = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}
message WeekByID               { int64 week_id   = 1; }
message WeeksForThreadRequest  { int64 thread_id = 1; }
message WeeksResponse          { repeated WeekResponse weeks = 1; }

// ========================================================
//  SCHEDULE
// ========================================================
message ThreadScheduleRequest {
  int64  thread_id    = 1;
  uint32 day_of_week  = 2;   // 1=Mon … 7=Sun
  string start_time   = 3;   // "HH:MM:SS"
  string end_time     = 4;   // "HH:MM:SS"
  string location     = 5;   // Cabinet/room name
}
message ThreadScheduleUpdateRequest {
  int64  id           = 1;
  int64  thread_id    = 2;
  uint32 day_of_week  = 3;
  string start_time   = 4;
  string end_time     = 5;
  string location     = 6;   // Cabinet/room name
}
message ThreadScheduleResponse {
  int64  id           = 1;
  int64  thread_id    = 2;
  uint32 day_of_week  = 3;
  string start_time   = 4;
  string end_time     = 5;
  string location     = 6;   // Cabinet/room name
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}
message ThreadScheduleByID     { int64 schedule_id = 1; }
message ThreadSchedulesRequest { int64 thread_id   = 1; }
message ThreadSchedulesResponse{ repeated ThreadScheduleResponse schedules = 1; }

// ========================================================
//  THREADS FOR USER (rich)
// ========================================================
message UserThreadsRequest  { int64 user_id = 1; }

message CourseInfo {
  int64  id               = 1;
  string title            = 2;
  string description      = 3;
  string banner_image_url = 4;
}
message SemesterInfo {
  int64  id         = 1;
  string name       = 2;
  google.protobuf.Timestamp start_date = 3;
  google.protobuf.Timestamp end_date   = 4;
}

message TeacherInfo {
  int64  id      = 1;
  string name    = 2;
  string surname = 3;
  string email   = 4;
}

message ThreadWithDetails {
  ThreadResponse thread   = 1;
  CourseInfo     course   = 2;
  SemesterInfo   semester = 3;
  TeacherInfo    teacher  = 4;
}

message UserThreadsResponse { repeated ThreadWithDetails threads = 1; }

message WeeksWithHwRequest {
  int64 thread_id = 1;  // обязательный
  int64 user_id   = 2;  // для кого подтягиваем submission/score
}

message AssignmentWithSubmission {
  assignmentpb.AssignmentResponse           assignment = 1;
  assignmentpb.AssignmentSubmissionResponse submission = 2;
}

message WeekWithHw {
  WeekResponse                         week        = 1; // ваш уже существующий message
  repeated AssignmentWithSubmission    assignments = 2;
}

message WeeksWithHwResponse {
  repeated WeekWithHw weeks = 1;
}

// ========================================================
//  LOCATION
// ========================================================
message LocationRequest {
  string name        = 1;
  string description = 2;
  int32  capacity    = 3;
}

message LocationUpdateRequest {
  int64  id          = 1;
  string name        = 2;
  string description = 3;
  int32  capacity    = 4;
}

message LocationResponse {
  int64  id          = 1;
  string name        = 2;
  string description = 3;
  int32  capacity    = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}

message LocationByID { int64 location_id = 1; }
message LocationEmptyRequest {}
message LocationsResponse { repeated LocationResponse locations = 1; }

message LocationAvailabilityRequest {
  int64  location_id = 1;
  uint32 day_of_week = 2;
  string start_time  = 3;
  string end_time    = 4;
  int64  exclude_schedule_id = 5; // Optional, to exclude a specific schedule when checking
}

message LocationAvailabilityResponse {
  bool available = 1;
}

// ========================================================
//  PRE-REQUISITES
// ========================================================
message CheckPrerequisitesRequest {
  int64 user_id = 1;
  int64 thread_id = 2;
}