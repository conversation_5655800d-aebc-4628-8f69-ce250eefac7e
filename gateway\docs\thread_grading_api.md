# Thread Final Grading API

## Overview
This document describes the new endpoint for grading final thread registrations and automatically creating transcript entries.

## Endpoint

### Grade Final Thread Registration
**POST** `/thread/grade-final`

Grades a final thread registration and automatically creates a transcript entry.

#### Request Body
```json
{
  "user_id": 1,
  "thread_id": 2,
  "final_grade": 85.5
}
```

#### Request Parameters
- `user_id` (integer, required): ID of the student to grade
- `thread_id` (integer, required): ID of the thread 
- `final_grade` (float, required): Final grade between 0 and 100

#### Response
```json
{
  "message": "Final grade updated successfully",
  "registration": {
    "user_id": 1,
    "thread_id": 2,
    "final_grade": 85.5
  }
}
```

#### Error Responses
- `400 Bad Request`: Invalid request body or validation errors
- `404 Not Found`: User is not registered for this thread
- `500 Internal Server Error`: Server error

## Automatic Transcript Entry Creation

When a final grade is successfully recorded, the system automatically:

1. **Updates the thread registration** with the final grade
2. **Creates a transcript entry** with the following details:
   - Grade letter (converted from numeric grade)
   - Grade points (converted from numeric grade)
   - Credits: Always set to 5
   - Course and semester information from the thread
   - Completion date: Current timestamp

### Grade Conversion

The system uses the following conversion tables:

#### Numeric to Letter Grade
- 95-100: A+
- 90-94: A
- 85-89: A-
- 80-84: B+
- 75-79: B
- 70-74: B-
- 65-69: C+
- 60-64: C
- 55-59: C-
- 50-54: D
- 0-49: F

#### Numeric to Grade Points (4.0 scale)
- 95-100: 4.0
- 90-94: 3.7
- 85-89: 3.3
- 80-84: 3.0
- 75-79: 2.7
- 70-74: 2.3
- 65-69: 2.0
- 60-64: 1.7
- 55-59: 1.3
- 50-54: 1.0
- 0-49: 0.0

## Example Usage

### Curl Example
```bash
curl -X POST http://localhost:8081/thread/grade-final \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin_token>" \
  -d '{
    "user_id": 1,
    "thread_id": 2,
    "final_grade": 85.5
  }'
```

### Expected Response
```json
{
  "message": "Final grade updated successfully",
  "registration": {
    "user_id": 1,
    "thread_id": 2,
    "final_grade": 85.5
  }
}
```

## Notes

- The transcript entry creation happens asynchronously in the background
- If transcript entry creation fails, the final grade is still recorded
- Errors in transcript creation are logged but don't affect the main response
- The system always sets credits to 5 for transcript entries as per requirements
- The transcript ID is currently hardcoded to 1 (TODO: implement proper transcript lookup)
