package thread

import (
	"context"
	"errors"

	"github.com/jackc/pgx/v4"
	"github.com/olzzhas/edunite-server/course_service/internal/dto"
	"github.com/olzzhas/edunite-server/course_service/internal/query/threadquery"
	threadpb "github.com/olzzhas/edunite-server/course_service/pb/thread"
	"google.golang.org/protobuf/types/known/timestamppb"

	"github.com/olzzhas/edunite-server/course_service/pkg/validator"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/olzzhas/edunite-server/course_service/internal/database"
	"google.golang.org/protobuf/types/known/emptypb"
)

type Service struct {
	threadRepo      database.ThreadRepository
	weekRepo        database.WeekRepository
	scheduleRepo    database.ThreadScheduleRepository
	locationRepo    database.LocationRepository
	threadQueryRepo threadquery.Repo
	courseRepo      database.CourseRepository
	threadpb.UnimplementedThreadServiceServer
}

func NewThreadService(
	threadRepo database.ThreadRepository,
	weekRepo database.WeekRepository,
	scheduleRepo database.ThreadScheduleRepository,
	locationRepo database.LocationRepository,
	threadQuery threadquery.Repo,
	courseRepo database.CourseRepository,
) *Service {
	return &Service{
		threadRepo:      threadRepo,
		weekRepo:        weekRepo,
		scheduleRepo:    scheduleRepo,
		locationRepo:    locationRepo,
		threadQueryRepo: threadQuery,
		courseRepo:      courseRepo,
	}
}

func (s *Service) CreateThread(ctx context.Context, req *threadpb.ThreadRequest) (*threadpb.ThreadResponse, error) {
	th := &database.Thread{
		CourseID:    req.GetCourseId(),
		SemesterID:  req.GetSemesterId(),
		TeacherID:   req.GetTeacherId(),
		MaxStudents: req.GetMaxStudents(),
		Title:       req.GetTitle(),
		SyllabusURL: req.GetSyllabusUrl(),
	}

	v := validator.New()
	database.ValidateThread(v, th)
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument,
			"validation failed: %v", v.Errors)
	}

	if err := s.threadRepo.CreateThread(ctx, th); err != nil {
		return nil, status.Errorf(codes.Internal,
			"could not create thread: %v", err)
	}
	return threadTothreadpb(th), nil
}

func (s *Service) ListThreadsByCourse(ctx context.Context, req *threadpb.ThreadsByCourseRequest) (*threadpb.ThreadsResponse, error) {
	list, err := s.threadRepo.GetThreadsByCourse(ctx, req.GetCourseId())
	if err != nil {
		return nil, status.Errorf(codes.Internal,
			"error listing threads: %v", err)
	}
	return threadsTothreadpb(list), nil
}

func (s *Service) ListThreads(ctx context.Context, req *threadpb.ThreadEmptyRequest) (*threadpb.ThreadsResponse, error) {
	list, err := s.threadRepo.GetAllThreads(ctx)
	if err != nil {
		return nil, status.Errorf(codes.Internal,
			"error listing threads: %v", err)
	}
	return threadsTothreadpb(list), nil
}

func (s *Service) GetThreadByID(ctx context.Context, req *threadpb.ThreadByID) (*threadpb.ThreadResponse, error) {
	th, err := s.threadRepo.GetThread(ctx, req.GetThreadId())
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, status.Errorf(codes.NotFound, "thread not found")
		}
		return nil, status.Errorf(codes.Internal,
			"error fetching thread: %v", err)
	}
	return threadTothreadpb(th), nil
}

func (s *Service) UpdateThreadByID(ctx context.Context, req *threadpb.ThreadUpdateRequest) (*threadpb.ThreadResponse, error) {
	ex, err := s.threadRepo.GetThread(ctx, req.GetId())
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, status.Errorf(codes.NotFound, "thread not found")
		}
		return nil, status.Errorf(codes.Internal,
			"error fetching thread: %v", err)
	}

	ex.CourseID = req.GetCourseId()
	ex.SemesterID = req.GetSemesterId()
	ex.TeacherID = req.GetTeacherId()
	ex.MaxStudents = req.GetMaxStudents()
	ex.Title = req.GetTitle()
	ex.SyllabusURL = req.GetSyllabusUrl()

	v := validator.New()
	database.ValidateThread(v, ex)
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument,
			"validation failed: %v", v.Errors)
	}

	if err := s.threadRepo.UpdateThread(ctx, ex); err != nil {
		return nil, status.Errorf(codes.Internal,
			"could not update thread: %v", err)
	}
	return threadTothreadpb(ex), nil
}

func (s *Service) DeleteThreadByID(ctx context.Context, req *threadpb.ThreadByID) (*threadpb.ThreadEmptyResponse, error) {
	if err := s.threadRepo.DeleteThread(ctx, req.GetThreadId()); err != nil {
		if err.Error() == "thread not found" {
			return nil, status.Errorf(codes.NotFound, "thread not found")
		}
		return nil, status.Errorf(codes.Internal,
			"could not delete thread: %v", err)
	}
	return &threadpb.ThreadEmptyResponse{}, nil
}

func threadTothreadpb(t *database.Thread) *threadpb.ThreadResponse {
	return &threadpb.ThreadResponse{
		Id:          t.ID,
		CourseId:    t.CourseID,
		SemesterId:  t.SemesterID,
		TeacherId:   t.TeacherID,
		MaxStudents: t.MaxStudents,
		Title:       t.Title,
		SyllabusUrl: t.SyllabusURL,
		CreatedAt:   timestamppb.New(t.CreatedAt),
		UpdatedAt:   timestamppb.New(t.UpdatedAt),
	}
}

func threadsTothreadpb(threads []*database.Thread) *threadpb.ThreadsResponse {
	threadpbThreads := make([]*threadpb.ThreadResponse, 0, len(threads))
	for _, t := range threads {
		threadpbThreads = append(threadpbThreads, threadTothreadpb(t))
	}
	return &threadpb.ThreadsResponse{Threads: threadpbThreads}
}

// RegisterUserToThread регистрирует одного пользователя в потоке
func (s *Service) RegisterUserToThread(ctx context.Context, req *threadpb.RegisterUserToThreadRequest) (*threadpb.ThreadEmptyResponse, error) {
	v := validator.New()
	v.Check(req.GetUserId() > 0, "user_id", "must be provided and > 0")
	v.Check(req.GetThreadId() > 0, "thread_id", "must be provided and > 0")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	thread, err := s.threadRepo.GetThread(ctx, req.GetThreadId())
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, status.Errorf(codes.NotFound, "thread not found")
		}
		return nil, status.Errorf(codes.Internal, "error fetching thread")
	}

	// Check prerequisites
	prerequisitesMet, err := s.courseRepo.CheckPrerequisites(ctx, thread.CourseID, req.GetUserId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "error checking prerequisites: %v", err)
	}
	if !prerequisitesMet {
		return nil, status.Errorf(codes.FailedPrecondition, "prerequisites not met")
	}

	err = s.threadRepo.RegisterUserToThread(ctx, req.GetUserId(), req.GetThreadId())
	if err != nil {
		switch {
		case errors.Is(err, database.ErrThreadFull):
			return nil, status.Errorf(codes.ResourceExhausted, err.Error())
		case errors.Is(err, database.ErrAlreadyRegistered):
			return nil, status.Errorf(codes.AlreadyExists, err.Error())
		default:
			return nil, status.Errorf(codes.Internal, "could not register user")
		}
	}

	return &threadpb.ThreadEmptyResponse{}, nil
}

// RegisterManyUsersToThread регистрирует нескольких пользователей в потоке
func (s *Service) RegisterManyUsersToThread(ctx context.Context, req *threadpb.RegisterManyUsersToThreadRequest) (*threadpb.ThreadEmptyResponse, error) {
	v := validator.New()
	v.Check(req.GetThreadId() > 0, "thread_id", "must be provided and > 0")
	v.Check(len(req.GetUserId()) > 0, "user_id", "must supply at least one user_id")
	v.Check(validator.Unique(req.GetUserId()), "user_id", "must not contain duplicates")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	if _, err := s.threadRepo.GetThread(ctx, req.GetThreadId()); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, status.Errorf(codes.NotFound, "thread not found")
		}
		return nil, status.Errorf(codes.Internal, "error fetching thread")
	}

	err := s.threadRepo.RegisterManyUsersToThread(ctx, req.GetThreadId(), req.GetUserId())
	if err != nil {
		switch {
		case errors.Is(err, database.ErrThreadFull):
			return nil, status.Errorf(codes.ResourceExhausted, err.Error())
		case errors.Is(err, database.ErrRegistrationExists):
			return nil, status.Errorf(codes.AlreadyExists, err.Error())
		default:
			return nil, status.Errorf(codes.Internal, "could not register users: %v", err)
		}
	}

	// 4) Успех
	return &threadpb.ThreadEmptyResponse{}, nil
}

// GradeFinalThreadRegistration grades a final thread registration and creates transcript entry
func (s *Service) GradeFinalThreadRegistration(ctx context.Context, req *threadpb.GradeFinalThreadRegistrationRequest) (*threadpb.GradeFinalThreadRegistrationResponse, error) {
	v := validator.New()
	v.Check(req.GetUserId() > 0, "user_id", "must be provided and > 0")
	v.Check(req.GetThreadId() > 0, "thread_id", "must be provided and > 0")
	v.Check(req.GetFinalGrade() >= 0 && req.GetFinalGrade() <= 100, "final_grade", "must be between 0 and 100")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	// Check if the registration exists
	registrations, err := s.threadRepo.ListThreadRegistrations(ctx, req.GetThreadId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "error fetching thread registrations: %v", err)
	}

	var registrationExists bool
	for _, reg := range registrations {
		if reg.UserID == req.GetUserId() {
			registrationExists = true
			break
		}
	}

	if !registrationExists {
		return nil, status.Errorf(codes.NotFound, "user is not registered for this thread")
	}

	// Update the final grade
	err = s.threadRepo.UpdateFinalGrade(ctx, req.GetUserId(), req.GetThreadId(), req.GetFinalGrade())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "could not update final grade: %v", err)
	}

	// Get thread information for transcript entry
	thread, err := s.threadRepo.GetThread(ctx, req.GetThreadId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "error fetching thread: %v", err)
	}

	// Return success response
	return &threadpb.GradeFinalThreadRegistrationResponse{
		Message: "Final grade updated successfully",
		Registration: &threadpb.ThreadRegistrationResponse{
			UserId:     req.GetUserId(),
			ThreadId:   req.GetThreadId(),
			FinalGrade: float32(req.GetFinalGrade()),
		},
	}, nil
}

// RemoveRegistrationToThread удаляет регистрацию пользователя из потока
func (s *Service) RemoveRegistrationToThread(ctx context.Context, req *threadpb.RemoveRegistrationToThreadRequest) (*threadpb.ThreadEmptyResponse, error) {
	v := validator.New()
	v.Check(req.GetUserId() > 0, "user_id", "must be provided and > 0")
	v.Check(req.GetThreadId() > 0, "thread_id", "must be provided and > 0")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	if err := s.threadRepo.RemoveRegistrationToThread(ctx, req.GetUserId(), req.GetThreadId()); err != nil {
		switch {
		case errors.Is(err, database.ErrRegistrationNotFound):
			return nil, status.Errorf(codes.NotFound, err.Error())
		default:
			return nil, status.Errorf(codes.Internal, "could not remove registration")
		}
	}
	return &threadpb.ThreadEmptyResponse{}, nil
}

// RemoveManyRegistrationsToThread удаляет регистрации нескольких пользователей из потока
func (s *Service) RemoveManyRegistrationsToThread(ctx context.Context, req *threadpb.RemoveManyRegistrationsToThreadRequest) (*threadpb.ThreadEmptyResponse, error) {
	v := validator.New()
	v.Check(req.GetThreadId() > 0, "thread_id", "must be provided and > 0")
	v.Check(len(req.GetUserId()) > 0, "user_id", "must supply at least one user_id")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument, "validation failed: %v", v.Errors)
	}

	if err := s.threadRepo.RemoveManyRegistrationsToThread(ctx, req.GetThreadId(), req.GetUserId()); err != nil {
		return nil, status.Errorf(codes.Internal, "could not remove registrations: %v", err)
	}
	return &threadpb.ThreadEmptyResponse{}, nil
}

func (s *Service) ListThreadRegistrations(ctx context.Context, req *threadpb.ThreadByID) (*threadpb.ListThreadRegistrationsResponse, error) {
	registrations, err := s.threadRepo.ListThreadRegistrations(ctx, req.GetThreadId())
	if err != nil {
		return nil, errors.New("failed to list thread registrations")
	}

	var respRegistrations []*database.ThreadRegistration
	for _, reg := range registrations {
		respRegistrations = append(respRegistrations, &database.ThreadRegistration{
			UserID:           reg.UserID,
			ThreadID:         reg.ThreadID,
			RegistrationDate: reg.RegistrationDate,
			FinalGrade:       reg.FinalGrade,
		})
	}

	var threadpbRegList []*threadpb.ThreadRegistrationResponse
	for _, reg := range registrations {
		threadpbRegList = append(threadpbRegList, &threadpb.ThreadRegistrationResponse{
			ThreadId:   reg.ThreadID,
			UserId:     reg.UserID,
			FinalGrade: float32(reg.FinalGrade),
		})
	}

	return &threadpb.ListThreadRegistrationsResponse{ThreadRegistrations: threadpbRegList}, nil
}

func (s *Service) ListThreadsForUser(ctx context.Context, req *threadpb.UserThreadsRequest) (*threadpb.UserThreadsResponse, error) {

	v := validator.New()
	v.Check(req.GetUserId() > 0, "user_id", "must be > 0")
	if !v.Valid() {
		return nil, status.Errorf(codes.InvalidArgument,
			"validation failed: %v", v.Errors)
	}

	recs, err := s.threadQueryRepo.ListThreadsForUser(ctx, req.GetUserId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "query failed: %v", err)
	}

	out := &threadpb.UserThreadsResponse{}
	for _, r := range recs {
		out.Threads = append(out.Threads, convertThreadWithRelations(r))
	}
	return out, nil
}

// helper
func convertThreadWithRelations(r *dto.ThreadWithRelations) *threadpb.ThreadWithDetails {
	return &threadpb.ThreadWithDetails{
		Thread: threadTothreadpb(&r.Thread),
		Course: &threadpb.CourseInfo{
			Id:             r.Course.ID,
			Title:          r.Course.Title,
			Description:    r.Course.Description,
			BannerImageUrl: r.Course.BannerImageUrl,
		},
		Semester: &threadpb.SemesterInfo{
			Id:        r.Semester.ID,
			Name:      r.Semester.Name,
			StartDate: timestamppb.New(r.Semester.StartDate),
			EndDate:   timestamppb.New(r.Semester.EndDate),
		},
	}
}

// CheckPrerequisites checks if a user has completed all prerequisites for a course
func (s *Service) CheckPrerequisites(ctx context.Context, req *threadpb.CheckPrerequisitesRequest) (*emptypb.Empty, error) {
	thread, err := s.threadRepo.GetThread(ctx, req.GetThreadId())
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, status.Errorf(codes.NotFound, "thread not found")
		}
		return nil, status.Errorf(codes.Internal, "error fetching thread: %v", err)
	}

	prerequisitesMet, err := s.courseRepo.CheckPrerequisites(ctx, thread.CourseID, req.GetUserId())
	if err != nil {
		return nil, status.Errorf(codes.Internal, "error checking prerequisites: %v", err)
	}
	if !prerequisitesMet {
		return nil, status.Errorf(codes.FailedPrecondition, "prerequisites not met")
	}

	return &emptypb.Empty{}, nil
}
