package clients

import (
	"context"

	transcriptpb "github.com/olzzhas/edunite-server/course_service/pb/transcript"
	"google.golang.org/grpc"
)

// TranscriptClient wraps the gRPC transcript service client
type TranscriptClient struct {
	client transcriptpb.TranscriptServiceClient
}

// NewTranscriptClient creates a new transcript client
func NewTranscriptClient(conn *grpc.ClientConn) *TranscriptClient {
	return &TranscriptClient{
		client: transcriptpb.NewTranscriptServiceClient(conn),
	}
}

// GetClient returns the underlying gRPC client
func (c *TranscriptClient) GetClient() transcriptpb.TranscriptServiceClient {
	return c.client
}

// AddTranscriptEntry adds a new entry to a transcript
func (c *TranscriptClient) AddTranscriptEntry(ctx context.Context, req *transcriptpb.AddTranscriptEntryRequest) (*transcriptpb.TranscriptEntryResponse, error) {
	return c.client.AddTranscriptEntry(ctx, req)
}
